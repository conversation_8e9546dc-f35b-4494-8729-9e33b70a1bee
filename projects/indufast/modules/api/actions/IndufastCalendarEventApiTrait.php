<?php

  use classes\ApiResponse;

  trait IndufastCalendarEventApiTrait {

    public function executeEventCreate(): void {
      $event = new IndufastCalendarEvent();
      $event->fill($this->data)->validateFillable();

      try {
        if (!is_null($this->data['employee_ids'] ?? null)) {
          $event->updateEmployees();
        }
        $event->save();

        ApiResponse::sendResponseOK('Event saved', $event);
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while saving.');
      }
    }

    public function executeEventUpdate(): void {
      if (!$event = IndufastCalendarEvent::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Event not found');
      }
      $event->fill($this->data)->validateFillable();

      try {
        if (!is_null($this->data['employee_ids'] ?? null)) {
          $event->updateEmployees();
        }
        $event->save();
        ApiResponse::sendResponseOK('Event saved', $event);
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while saving.', (DEVELOPMENT) ? $e->getMessage() : '');
      }
    }

    public function executeEventDelete(): void {
      if (!$event = IndufastCalendarEvent::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Event not found');
      }

      try {
        $event->destroy();
        ApiResponse::sendResponseOK('Event deleted', $event);
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while deleting.');
      }
    }

    public function executeEventGetGoogleFiles(): void {
      // Support both single ID and array of IDs
      $eventIds = $_GET['id'] ?? $_GET['ids'] ?? null;

      if (!$eventIds) {
        ApiResponse::sendResponseError('Event ID(s) required');
        return;
      }

      // Convert single ID to array for consistent handling
      if (!is_array($eventIds)) {
        $eventIds = [$eventIds];
      }

      try {
        $events = IndufastCalendarEvent::find_all_by(['id' => $eventIds]);

        if (empty($events)) {
          ApiResponse::sendResponseNotFound('Calendar event(s) not found');
          return;
        }

        $filesByEvent = [];
        foreach ($events as $event) {
          $filesByEvent[$event->id] = $event->getGoogleFiles();
        }

        // If single ID was requested, return just the files array for backward compatibility
        if (count($eventIds) === 1 && isset($_GET['id'])) {
          $files = reset($filesByEvent);
          ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $files);
        } else {
          // Return grouped by event ID
          ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $filesByEvent);
        }
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while retrieving files.');
      }
    }

  }